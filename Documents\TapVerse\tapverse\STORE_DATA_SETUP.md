# TapVerse Store Data Setup

## Sample Store Data for Firestore

You need to manually add this data to your Firestore database to populate the store.

### 1. Store Items Collection

Go to Firebase Console > Firestore Database > Start Collection

**Collection ID**: `store`
**Document ID**: `items`

**Document Data**:
```json
{
  "neon_blue_skin": {
    "name": "Neon Blue",
    "description": "Electric blue glow effect",
    "type": "skin",
    "cost": 150,
    "category": "skins",
    "rarity": "common",
    "imageUrl": "assets/images/skins/neon_blue.png"
  },
  "fire_red_skin": {
    "name": "Fire Red",
    "description": "Blazing red flame effect",
    "type": "skin",
    "cost": 200,
    "category": "skins",
    "rarity": "rare",
    "imageUrl": "assets/images/skins/fire_red.png"
  },
  "cosmic_purple_skin": {
    "name": "Cosmic Purple",
    "description": "Galaxy swirl effect",
    "type": "skin",
    "cost": 300,
    "category": "skins",
    "rarity": "epic",
    "imageUrl": "assets/images/skins/cosmic_purple.png"
  },
  "crown_flair": {
    "name": "Golden Crown",
    "description": "Royal golden crown",
    "type": "flair",
    "cost": 250,
    "category": "flair",
    "rarity": "rare",
    "imageUrl": "assets/images/flair/crown.png"
  },
  "lightning_flair": {
    "name": "Lightning Bolt",
    "description": "Electric lightning effect",
    "type": "flair",
    "cost": 180,
    "category": "flair",
    "rarity": "common",
    "imageUrl": "assets/images/flair/lightning.png"
  },
  "diamond_flair": {
    "name": "Diamond Sparkle",
    "description": "Sparkling diamond effect",
    "type": "flair",
    "cost": 400,
    "category": "flair",
    "rarity": "legendary",
    "imageUrl": "assets/images/flair/diamond.png"
  }
}
```

### 2. Boost Items Collection

**Collection ID**: `store`
**Document ID**: `boosts`

**Document Data**:
```json
{
  "double_tokens": {
    "name": "Double Tokens",
    "description": "2x token rewards for 30 minutes",
    "type": "boost",
    "cost": 100,
    "duration": 30,
    "multiplier": 2,
    "category": "token_boost",
    "imageUrl": "assets/images/boosts/double_tokens.png"
  },
  "triple_tokens": {
    "name": "Triple Tokens",
    "description": "3x token rewards for 15 minutes",
    "type": "boost",
    "cost": 200,
    "duration": 15,
    "multiplier": 3,
    "category": "token_boost",
    "imageUrl": "assets/images/boosts/triple_tokens.png"
  },
  "mega_tokens": {
    "name": "Mega Tokens",
    "description": "5x token rewards for 10 minutes",
    "type": "boost",
    "cost": 350,
    "duration": 10,
    "multiplier": 5,
    "category": "token_boost",
    "imageUrl": "assets/images/boosts/mega_tokens.png"
  },
  "slow_motion": {
    "name": "Slow Motion",
    "description": "Slow down time for easier gameplay",
    "type": "boost",
    "cost": 150,
    "duration": 20,
    "effect": "slow_motion",
    "category": "gameplay_boost",
    "imageUrl": "assets/images/boosts/slow_motion.png"
  },
  "shield": {
    "name": "Shield",
    "description": "Protection from one mistake",
    "type": "boost",
    "cost": 120,
    "duration": 60,
    "effect": "shield",
    "category": "gameplay_boost",
    "imageUrl": "assets/images/boosts/shield.png"
  }
}
```

## Steps to Add Data:

1. **Go to Firebase Console**: https://console.firebase.google.com/project/tapverse-b5d48/firestore
2. **Click "Start collection"**
3. **Collection ID**: `store`
4. **Document ID**: `items`
5. **Copy and paste the items JSON above**
6. **Click "Save"**
7. **Add another document with ID**: `boosts`
8. **Copy and paste the boosts JSON above**
9. **Click "Save"**

## Usage in Your App:

```dart
// Get store items
final items = await StoreService.getStoreItems();

// Get boost items  
final boosts = await StoreService.getBoostItems();

// Purchase an item
final result = await StoreService.purchaseItem(
  itemId: 'neon_blue_skin',
  itemType: 'skin',
  cost: 150,
);

// Award tokens after game
final awardResult = await StoreService.awardTokens(
  gameId: 'tap_master',
  score: 1250,
  tokensEarned: 25,
);

// Activate a boost
final boostResult = await StoreService.activateBoost(
  boostId: 'double_tokens',
  durationMinutes: 30,
);
```

## Security Notes:

⚠️ **Current Limitations** (will be fixed with Cloud Functions):
- Users can modify their token balance directly in Firestore
- No server-side purchase validation
- No mystery box mechanics
- No secure daily rewards

✅ **Current Security** (from Firestore rules):
- Store items are read-only
- Users can only access their own data
- No duplicate purchases allowed
- Purchase history is immutable
- Boost restrictions enforced
